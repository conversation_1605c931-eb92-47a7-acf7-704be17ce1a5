"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";

import { upsertUser } from "@/lib/db/users";
import { createClient } from "@/lib/supabase/server";

/**
 * Server action to handle user authentication and database sync
 */
export async function handleAuthCallback() {
  const supabase = await createClient();

  try {
    // Get the current user from Supabase
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      console.error("Auth error:", error);
      redirect("/auth/login?error=Authentication failed");
    }

    // Sync user with database
    await upsertUser(user);

    // Revalidate any cached user data
    revalidatePath("/", "layout");

    return { success: true, user };
  } catch (error) {
    console.error("Error in auth callback:", error);
    redirect("/auth/login?error=Database sync failed");
  }
}

/**
 * Server action to sign out user
 */
export async function signOutUser() {
  const supabase = await createClient();

  try {
    const { error } = await supabase.auth.signOut();

    if (error) {
      throw error;
    }

    revalidatePath("/", "layout");
    redirect("/auth/login");
  } catch (error) {
    console.error("Sign out error:", error);
    redirect("/auth/login?error=Sign out failed");
  }
}

/**
 * Get current authenticated user with database profile
 */
export async function getCurrentUser() {
  const supabase = await createClient();

  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return null;
    }

    // Ensure user exists in database
    const dbUser = await upsertUser(user);

    return {
      ...user,
      profile: dbUser,
    };
  } catch (error) {
    console.error("Error getting current user:", error);
    return null;
  }
}

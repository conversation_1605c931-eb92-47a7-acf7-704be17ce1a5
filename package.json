{"name": "gramm_to_kcal", "version": "0.1.0", "private": true, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "test": "vitest run", "lint": "eslint", "lint:fix": "eslint --fix", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-router": "^1.128.3", "@tanstack/react-router-devtools": "^1.128.3", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.3", "embla-carousel-react": "^8.6.0", "idb": "^8.0.3", "input-otp": "^1.4.2", "lucide-react": "^0.525.0", "next": "15.4.1", "next-themes": "^0.4.6", "postgres": "^3.4.7", "react": "19.1.0", "react-day-picker": "^9.8.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-resizable-panels": "^3.0.3", "recharts": "2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^4.0.5"}, "devDependencies": {"@eslint-react/eslint-plugin": "^1.52.3", "@sxzz/eslint-config": "^7.0.6", "@tailwindcss/postcss": "^4", "@tanstack/eslint-plugin-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.83.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^17.2.0", "drizzle-kit": "^0.31.4", "eslint": "^9.31.0", "eslint-config-next": "15.4.1", "eslint-plugin-format": "^1.0.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "typescript": "^5", "vitest": "^3.2.4"}}